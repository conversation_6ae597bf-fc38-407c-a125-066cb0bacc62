//
//  SimpleMyPromotionViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by AI Assistant on 2025/3/26.
//

import UIKit
import JXSegmentedView

class SimpleMyPromotionViewController: UIViewController {
    
    // MARK: - UI Components
    
    // 背景图片容器视图
    private lazy var backgroundContainerView: UIView = {
        let view = UIView()
        view.clipsToBounds = true
        // 设置底部圆角
        view.layer.cornerRadius = 16
        view.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner] // 只设置底部圆角
        return view
    }()

    // 背景图片视图
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "commerce_module_profile_header_background_image")
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        return imageView
    }()
    
    // 自定义导航栏
    private lazy var customNavigationBar: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 透明背景，让背景图透过

        // 返回按钮
        let backButton = UIButton(type: .custom)
        backButton.setImage(UIImage(systemName: "chevron.left"), for: .normal)
        backButton.tintColor = .white
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        view.addSubview(backButton)

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "个人中心"
        titleLabel.textColor = .white
        titleLabel.font = UIFont.boldSystemFont(ofSize: 18)
        titleLabel.textAlignment = .center
        view.addSubview(titleLabel)

        // 设置约束
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
        }

        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        return view
    }()
    
    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .never
        return scrollView
    }()

    // 内容容器视图
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 用户信息区域
    private lazy var userInfoView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear

        // 头像
        view.addSubview(avatarImageView)
        // 用户名
        view.addSubview(nameLabel)
        // 等级标签容器
        view.addSubview(levelTagsContainer)
        // 粉丝数和作品数
        view.addSubview(fansLabel)
        view.addSubview(worksLabel)
        // 位置图标
        view.addSubview(locationButton)

        return view
    }()

    // 头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "default_avatar")
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 40 // 80*80 的一半
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor.white.cgColor
        return imageView
    }()

    // 用户名
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = "陈佳佳"
        label.textColor = .white
        label.font = UIFont.boldSystemFont(ofSize: 18)
        return label
    }()

    // 等级标签容器
    private lazy var levelTagsContainer: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.alignment = .center

        // 创建等级标签
        let levelTag1 = createLevelTag(text: "优质带货人")
        let levelTag2 = createLevelTag(text: "带货达人")

        stackView.addArrangedSubview(levelTag1)
        stackView.addArrangedSubview(levelTag2)

        return stackView
    }()

    // 粉丝数标签
    private lazy var fansLabel: UILabel = {
        let label = UILabel()
        label.text = "粉丝 1280"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14)
        return label
    }()

    // 作品数标签
    private lazy var worksLabel: UILabel = {
        let label = UILabel()
        label.text = "获赞 158.9w"
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14)
        return label
    }()

    // 设置按钮
    private lazy var locationButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "commerce_module_profile_settings_button_icon"), for: .normal)
        button.addTarget(self, action: #selector(settingsButtonTapped), for: .touchUpInside)
        return button
    }()

    // 数据统计卡片
    private lazy var statsCardView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 8 // 圆角8pt
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 1
        view.layer.shadowRadius = 8

        // 创建统计项
        let stats = [
            ("总销售额", "¥892,651"),
            ("本月销售", "¥56,892"),
            ("待结算", "¥12,368")
        ]

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        view.addSubview(stackView)

        for (index, stat) in stats.enumerated() {
            let containerView = UIView()

            let titleLabel = UILabel()
            titleLabel.text = stat.0
            titleLabel.textColor = UIColor(hex: "#999999")
            titleLabel.font = UIFont.systemFont(ofSize: 12)
            titleLabel.textAlignment = .center
            containerView.addSubview(titleLabel)

            let valueLabel = UILabel()
            valueLabel.text = stat.1
            valueLabel.textColor = UIColor(hex: "#333333")
            valueLabel.font = UIFont.boldSystemFont(ofSize: 18)
            valueLabel.textAlignment = .center
            containerView.addSubview(valueLabel)

            // 设置约束
            valueLabel.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(16)
                make.centerX.equalToSuperview()
            }

            titleLabel.snp.makeConstraints { make in
                make.top.equalTo(valueLabel.snp.bottom).offset(4)
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(-16)
            }

            stackView.addArrangedSubview(containerView)

            // 添加分隔线（除了最后一个）
            if index < stats.count - 1 {
                let separatorLine = UIView()
                separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
                containerView.addSubview(separatorLine)
                separatorLine.snp.makeConstraints { make in
                    make.right.equalToSuperview()
                    make.centerY.equalToSuperview()
                    make.width.equalTo(0.5)
                    make.height.equalTo(40)
                }
            }
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        return view
    }()

    // 功能按钮区域
    private lazy var functionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear

        // 创建3个独立的功能卡片
        let functions = [
            ("商品管理", "bag"),
            ("提现中心", "creditcard"),
            ("客服中心", "headphones")
        ]

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 12 // 卡片之间的间距
        view.addSubview(stackView)

        for (index, function) in functions.enumerated() {
            let cardView = createFunctionCard(title: function.0, iconName: function.1, tag: index)
            stackView.addArrangedSubview(cardView)
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        return view
    }()

    // 推荐商品区域
    private lazy var recommendedProductsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white

        // 标题区域
        let titleContainer = UIView()
        view.addSubview(titleContainer)

        let titleLabel = UILabel()
        titleLabel.text = "推荐商品"
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = UIFont.boldSystemFont(ofSize: 16)
        titleContainer.addSubview(titleLabel)

        let moreButton = UIButton(type: .custom)
        moreButton.setTitle("查看全部", for: .normal)
        moreButton.setTitleColor(UIColor(hex: "#999999"), for: .normal)
        moreButton.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        moreButton.setImage(UIImage(systemName: "chevron.right"), for: .normal)
        moreButton.tintColor = UIColor(hex: "#999999")
        moreButton.semanticContentAttribute = .forceRightToLeft
        moreButton.imageEdgeInsets = UIEdgeInsets(top: 0, left: 4, bottom: 0, right: -4)
        titleContainer.addSubview(moreButton)

        titleLabel.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
        }

        moreButton.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
        }

        titleContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        // 商品网格
        view.addSubview(productsCollectionView)
        productsCollectionView.snp.makeConstraints { make in
            make.top.equalTo(titleContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(600) // 固定高度，显示2行商品
        }

        return view
    }()

    // 商品集合视图
    private lazy var productsCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 16, right: 16)

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsVerticalScrollIndicator = false
        collectionView.isScrollEnabled = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ProductCollectionViewCell.self, forCellWithReuseIdentifier: "ProductCell")

        return collectionView
    }()

    // 商品数据
    private let products = [
        ProductModel(
            id: "1",
            title: "2025春季新款连衣裙 气质淑女收腰显瘦",
            price: "¥499",
            originalPrice: "¥699",
            commission: "¥20",
            imageURL: "product1"
        ),
        ProductModel(
            id: "2",
            title: "法国进口高端护肤精华生活补水保湿提亮",
            price: "¥688",
            originalPrice: "¥888",
            commission: "¥20",
            imageURL: "product2"
        ),
        ProductModel(
            id: "3",
            title: "2025新款法国进口香水 持久留香女士香水",
            price: "¥299",
            originalPrice: "¥399",
            commission: "¥20",
            imageURL: "product3"
        ),
        ProductModel(
            id: "4",
            title: "2025新款轻奢包包 真皮女士手提包",
            price: "¥688",
            originalPrice: "¥888",
            commission: "¥20",
            imageURL: "product4"
        )
    ]
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置视图背景色
        view.backgroundColor = .white
        
        // 设置UI
        setupUI()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 背景图片不需要特殊处理
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 添加背景图片容器 - 从屏幕最顶部开始，包括状态栏
        view.addSubview(backgroundContainerView)
        backgroundContainerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(239) // 背景图高度为239pt
        }

        // 添加背景图片到容器中
        backgroundContainerView.addSubview(backgroundImageView)
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 添加自定义导航栏 - 在安全区域内，覆盖在背景图上
        view.addSubview(customNavigationBar)
        customNavigationBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        // 添加滚动视图
        view.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        // 添加各个区域
        scrollContentView.addSubview(userInfoView)
        scrollContentView.addSubview(statsCardView)
        scrollContentView.addSubview(functionsView)
        scrollContentView.addSubview(recommendedProductsView)

        setupConstraints()
        setupUserInfoConstraints()
    }

    private func setupConstraints() {
        // 滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(customNavigationBar.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        // 滚动内容视图约束
        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 用户信息区域约束
        userInfoView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16) // 距离自定义导航栏下方16pt
            make.left.right.equalToSuperview()
            make.height.equalTo(120)
        }

        // 统计卡片约束
        statsCardView.snp.makeConstraints { make in
            make.top.equalTo(userInfoView.snp.bottom).offset(14) // 距离头像下方14pt
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(76) // 高度76pt
        }

        // 功能按钮约束
        functionsView.snp.makeConstraints { make in
            make.top.equalTo(statsCardView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(70) // 高度70pt
        }

        // 推荐商品约束
        recommendedProductsView.snp.makeConstraints { make in
            make.top.equalTo(functionsView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }

    private func setupUserInfoConstraints() {
        // 头像约束 - 80*80，左边距16pt
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.top.equalToSuperview()
            make.width.height.equalTo(80)
        }

        // 用户名约束 - 距离头像右侧12pt，顶部距离头像顶部2pt，高度26pt
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView.snp.top).offset(2)
            make.height.equalTo(26)
        }

        // 等级标签约束
        levelTagsContainer.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(6)
        }

        // 粉丝数约束
        fansLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.bottom.equalTo(avatarImageView.snp.bottom).offset(-4)
        }

        // 作品数约束
        worksLabel.snp.makeConstraints { make in
            make.left.equalTo(fansLabel.snp.right).offset(20)
            make.centerY.equalTo(fansLabel)
        }

        // 设置按钮约束 - 替换位置按钮
        locationButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.top.equalTo(avatarImageView.snp.top)
            make.width.height.equalTo(20) // 20*20pt
        }
    }

    // MARK: - Helper Methods

    private func createLevelTag(text: String) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        containerView.layer.cornerRadius = 10
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.white.cgColor

        let label = UILabel()
        label.text = text
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 10)
        label.textAlignment = .center
        containerView.addSubview(label)

        label.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 8, bottom: 4, right: 8))
        }

        return containerView
    }

    private func createFunctionCard(title: String, iconName: String, tag: Int) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = .white
        cardView.layer.cornerRadius = 8 // 圆角8pt
        cardView.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        cardView.layer.shadowOffset = CGSize(width: 0, height: 2)
        cardView.layer.shadowOpacity = 1
        cardView.layer.shadowRadius = 4

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: iconName)
        iconImageView.tintColor = UIColor(hex: "#666666")
        iconImageView.contentMode = .scaleAspectFit
        cardView.addSubview(iconImageView)

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = UIFont.systemFont(ofSize: 14)
        titleLabel.textAlignment = .center
        cardView.addSubview(titleLabel)

        iconImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(24)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-16)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(functionButtonTapped(_:)))
        cardView.addGestureRecognizer(tapGesture)
        cardView.isUserInteractionEnabled = true
        cardView.tag = tag

        return cardView
    }

    // MARK: - Actions

    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }

    @objc private func settingsButtonTapped() {
        print("点击了设置按钮")
        // TODO: 实现设置功能
    }

    @objc private func functionButtonTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let functions = ["商品管理", "提现中心", "客服中心"]
        let functionName = functions[view.tag]
        print("点击了: \(functionName)")
        // TODO: 实现具体功能
    }
}

// MARK: - UICollectionViewDataSource

extension SimpleMyPromotionViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return products.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ProductCell", for: indexPath) as! ProductCollectionViewCell
        cell.configure(with: products[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension SimpleMyPromotionViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let padding: CGFloat = 16 * 2 + 12 // 左右边距 + 中间间距
        let availableWidth = collectionView.frame.width - padding
        let itemWidth = availableWidth / 2
        return CGSize(width: itemWidth, height: 280)
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate

extension SimpleMyPromotionViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }

    func listDidAppear() {
        print("我的带货页面显示")
    }

    func listDidDisappear() {
        print("我的带货页面隐藏")
    }
}
